import * as admin from 'firebase-admin';

// Função para inicializar o Firebase Admin de forma lazy
export function initializeFirebaseAdmin(): admin.app.App {
  if (admin.apps.length > 0) {
    return admin.app();
  }

  try {
    console.log('🔥 Inicializando Firebase Admin...');

    // Em desenvolvimento local, usar credenciais das variáveis de ambiente
    if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_ADMIN_PRIVATE_KEY) {
      console.log('💻 Usando credenciais de desenvolvimento');
      const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n');

      return admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: privateKey,
        }),
        databaseURL: 'https://woomanagerapp-77606-default-rtdb.europe-west1.firebasedatabase.app',
        storageBucket: 'woomanagerapp-77606.firebasestorage.app',
      });
    } else {
      // Em produção, usar credenciais padrão do ambiente
      console.log('🚀 Usando credenciais padrão do ambiente');
      return admin.initializeApp();
    }

  } catch (error) {
    console.error('❌ Erro ao inicializar Firebase Admin:', error);
    throw error;
  }
}

// Funções para obter instâncias do Firebase Admin
export function getAdminAuth(): admin.auth.Auth {
  const app = initializeFirebaseAdmin();
  return admin.auth(app);
}

export function getAdminFirestore(): admin.firestore.Firestore {
  const app = initializeFirebaseAdmin();
  return admin.firestore(app);
}

export function getAdminStorage(): admin.storage.Storage {
  const app = initializeFirebaseAdmin();
  return admin.storage(app);
}

export default admin;
