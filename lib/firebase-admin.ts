import * as admin from 'firebase-admin';

// Interface para configuração do Firebase Admin
interface FirebaseAdminConfig {
  projectId: string;
  clientEmail: string;
  privateKey: string;
  databaseURL: string;
  storageBucket: string;
}

// Variável global para armazenar a instância da app
let firebaseAdminApp: admin.app.App | null = null;

// Função para obter configuração do Firebase Admin
function getFirebaseAdminConfig(): FirebaseAdminConfig | null {
  const projectId = process.env.FIREBASE_PROJECT_ID;
  const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
  const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY;
  const databaseURL = process.env.FIREBASE_DATABASE_URL || 'https://woomanagerapp-77606-default-rtdb.europe-west1.firebasedatabase.app';
  const storageBucket = process.env.FIREBASE_STORAGE_BUCKET || 'woomanagerapp-77606.firebasestorage.app';

  if (!projectId || !clientEmail || !privateKey) {
    return null;
  }

  return {
    projectId,
    clientEmail,
    privateKey: privateKey.replace(/\\n/g, '\n'),
    databaseURL,
    storageBucket,
  };
}

// Função para verificar se estamos em ambiente de produção Firebase Functions
function isFirebaseFunctions(): boolean {
  return !!(
    process.env.FUNCTION_NAME ||
    process.env.K_SERVICE ||
    process.env.GCLOUD_PROJECT
  );
}

// Função para inicializar o Firebase Admin de forma robusta
export function initializeFirebaseAdmin(): admin.app.App {
  try {
    // Se já temos uma instância, retornar ela
    if (firebaseAdminApp) {
      console.log('🔄 Reutilizando instância existente do Firebase Admin');
      return firebaseAdminApp;
    }

    // Se já existe uma app inicializada, usar ela
    if (admin.apps.length > 0) {
      console.log('🔄 Usando app Firebase Admin já inicializada');
      firebaseAdminApp = admin.app();
      return firebaseAdminApp;
    }

    console.log('🔥 Inicializando Firebase Admin...');
    console.log('🌍 Ambiente detectado:', {
      nodeEnv: process.env.NODE_ENV,
      isFirebaseFunctions: isFirebaseFunctions(),
      gcloudProject: process.env.GCLOUD_PROJECT,
      functionName: process.env.FUNCTION_NAME,
      kService: process.env.K_SERVICE,
    });

    const config = getFirebaseAdminConfig();

    if (config) {
      // Usar credenciais explícitas (desenvolvimento ou produção com variáveis)
      console.log('🔑 Usando credenciais explícitas das variáveis de ambiente');
      firebaseAdminApp = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: config.projectId,
          clientEmail: config.clientEmail,
          privateKey: config.privateKey,
        }),
        databaseURL: config.databaseURL,
        storageBucket: config.storageBucket,
      });
    } else if (isFirebaseFunctions()) {
      // Em Firebase Functions, usar credenciais padrão do ambiente
      console.log('🚀 Usando credenciais padrão do Firebase Functions');
      firebaseAdminApp = admin.initializeApp({
        projectId: 'woomanagerapp-77606',
        databaseURL: 'https://woomanagerapp-77606-default-rtdb.europe-west1.firebasedatabase.app',
        storageBucket: 'woomanagerapp-77606.firebasestorage.app',
      });
    } else {
      // Fallback: tentar inicializar sem configuração
      console.log('⚠️ Tentando inicialização sem configuração explícita');
      firebaseAdminApp = admin.initializeApp();
    }

    console.log('✅ Firebase Admin inicializado com sucesso');
    console.log('📊 Apps inicializadas:', admin.apps.length);
    console.log('🆔 App name:', firebaseAdminApp.name);

    return firebaseAdminApp;

  } catch (error) {
    console.error('❌ Erro ao inicializar Firebase Admin:', error);

    // Reset da variável global em caso de erro
    firebaseAdminApp = null;

    // Tentar estratégias alternativas
    try {
      console.log('🔄 Tentando estratégias alternativas de inicialização...');

      // Estratégia 1: Inicialização mínima para Firebase Functions
      if (isFirebaseFunctions()) {
        console.log('🔄 Estratégia 1: Inicialização mínima para Firebase Functions');
        firebaseAdminApp = admin.initializeApp({
          projectId: 'woomanagerapp-77606',
        });
        console.log('✅ Estratégia 1 bem-sucedida');
        return firebaseAdminApp;
      }

      // Estratégia 2: Inicialização completamente padrão
      console.log('🔄 Estratégia 2: Inicialização completamente padrão');
      firebaseAdminApp = admin.initializeApp();
      console.log('✅ Estratégia 2 bem-sucedida');
      return firebaseAdminApp;

    } catch (emergencyError) {
      console.error('💥 Todas as estratégias de inicialização falharam:', emergencyError);
      throw new Error(`Falha na inicialização do Firebase Admin: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
}

// Funções para obter instâncias do Firebase Admin com tratamento de erro melhorado
export function getAdminAuth(): admin.auth.Auth {
  try {
    console.log('🔐 Obtendo instância do Firebase Auth...');
    const app = initializeFirebaseAdmin();
    const auth = admin.auth(app);
    console.log('✅ Instância do Firebase Auth obtida com sucesso');
    return auth;
  } catch (error) {
    console.error('❌ Erro ao obter instância do Auth:', error);
    console.error('📊 Status atual:', getFirebaseAdminStatus());
    throw new Error(`Falha ao inicializar Firebase Auth Admin: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

export function getAdminFirestore(): admin.firestore.Firestore {
  try {
    console.log('🗄️ Obtendo instância do Firestore...');
    const app = initializeFirebaseAdmin();
    const firestore = admin.firestore(app);
    console.log('✅ Instância do Firestore obtida com sucesso');
    return firestore;
  } catch (error) {
    console.error('❌ Erro ao obter instância do Firestore:', error);
    console.error('📊 Status atual:', getFirebaseAdminStatus());
    throw new Error(`Falha ao inicializar Firebase Firestore Admin: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

export function getAdminStorage(): admin.storage.Storage {
  try {
    console.log('📦 Obtendo instância do Storage...');
    const app = initializeFirebaseAdmin();
    const storage = admin.storage(app);
    console.log('✅ Instância do Storage obtida com sucesso');
    return storage;
  } catch (error) {
    console.error('❌ Erro ao obter instância do Storage:', error);
    console.error('📊 Status atual:', getFirebaseAdminStatus());
    throw new Error(`Falha ao inicializar Firebase Storage Admin: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

// Função para verificar o status da inicialização
export function getFirebaseAdminStatus() {
  try {
    return {
      isInitialized: firebaseAdminApp !== null,
      appsCount: admin.apps.length,
      hasCredentials: getFirebaseAdminConfig() !== null,
      isFirebaseFunctions: isFirebaseFunctions(),
      appName: firebaseAdminApp?.name || 'N/A',
      environment: {
        nodeEnv: process.env.NODE_ENV,
        gcloudProject: process.env.GCLOUD_PROJECT,
        functionName: process.env.FUNCTION_NAME,
        kService: process.env.K_SERVICE,
      }
    };
  } catch (error) {
    console.error('❌ Erro ao obter status do Firebase Admin:', error);
    return {
      isInitialized: false,
      appsCount: 0,
      hasCredentials: false,
      isFirebaseFunctions: isFirebaseFunctions(),
      appName: 'ERROR',
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      environment: {
        nodeEnv: process.env.NODE_ENV,
        gcloudProject: process.env.GCLOUD_PROJECT,
        functionName: process.env.FUNCTION_NAME,
        kService: process.env.K_SERVICE,
      }
    };
  }
}

export default admin;
