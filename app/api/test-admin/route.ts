import { NextResponse } from 'next/server';
import { getAdminAuth, getAdminFirestore } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';

export async function GET() {
  console.log("=== 🧪 API de teste do Firebase Admin chamada ===");
  console.log("🌍 Ambiente:", {
    nodeEnv: process.env.NODE_ENV,
    gcloudProject: process.env.GCLOUD_PROJECT,
    functionName: process.env.FUNCTION_NAME,
    kService: process.env.K_SERVICE
  });

  try {

    // Testar funcionalidades básicas
    console.log("🧪 Testando funcionalidades do Firebase Admin...");

    // Testar funcionalidades básicas
    console.log("🧪 Testando funcionalidades do Firebase Admin...");

    try {
      // Testar Auth
      console.log("🔐 Testando Auth...");
      const auth = getAdminAuth();
      const authTest = await auth.listUsers(1);
      console.log("✅ Auth funcionando - usuários encontrados:", authTest.users.length);

      // Testar Firestore
      console.log("🗄️ Testando Firestore...");
      const firestore = getAdminFirestore();
      const firestoreTest = await firestore.collection('test').limit(1).get();
      console.log("✅ Firestore funcionando - documentos encontrados:", firestoreTest.size);

      return NextResponse.json({
        success: true,
        message: 'Firebase Admin SDK está funcionando corretamente! 🎉',
        tests: {
          auth: `${authTest.users.length} usuários encontrados`,
          firestore: `${firestoreTest.size} documentos de teste encontrados`
        },
        environment: {
          nodeEnv: process.env.NODE_ENV,
          gcloudProject: process.env.GCLOUD_PROJECT,
          appsInitialized: admin.apps.length
        }
      });
    } catch (testError) {
      console.error("❌ Erro nos testes:", testError);
      return NextResponse.json(
        {
          error: 'Erro ao testar funcionalidades do Firebase Admin',
          details: testError instanceof Error ? testError.message : 'Erro desconhecido',
          environment: {
            nodeEnv: process.env.NODE_ENV,
            gcloudProject: process.env.GCLOUD_PROJECT,
            appsInitialized: admin.apps.length
          }
        },
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Erro no teste do Firebase Admin:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    return NextResponse.json(
      { error: 'Erro no teste do Firebase Admin', details: errorMessage },
      { status: 500 }
    );
  }
}
