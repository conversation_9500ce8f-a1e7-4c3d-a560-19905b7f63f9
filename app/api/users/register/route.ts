import { NextRequest, NextResponse } from 'next/server';
import { getAdminAuth, getAdminFirestore, getFirebaseAdminStatus } from '@/lib/firebase-admin';
import * as admin from 'firebase-admin';
import { UserRole } from '@/types/team';

export async function POST(request: NextRequest) {
  console.log("=== 🚀 API de registo de utilizador chamada ===");

  try {
    // Verificar status do Firebase Admin
    const adminStatus = getFirebaseAdminStatus();
    console.log("📊 Status do Firebase Admin:", adminStatus);

    if (!adminStatus.isInitialized) {
      console.error("❌ Firebase Admin não está inicializado");
      return NextResponse.json(
        {
          error: 'Firebase Admin não está inicializado',
          status: adminStatus
        },
        { status: 500 }
      );
    }

    const body = await request.json();
    console.log("📝 Dados recebidos para registo:", {
      email: body.email,
      fullName: body.fullName,
      role: body.role,
      teamId: body.teamId
    });
    const { email, password, fullName, registrationNumber, category, role, teamId, canEditProfile } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email e password são obrigatórios' },
        { status: 400 }
      );
    }

    // Obter instâncias do Firebase Admin com tratamento de erro
    let auth: admin.auth.Auth;
    let firestore: admin.firestore.Firestore;

    try {
      console.log("🔐 Obtendo instância do Firebase Auth...");
      auth = getAdminAuth();
      console.log("✅ Firebase Auth obtido com sucesso");

      console.log("🗄️ Obtendo instância do Firestore...");
      firestore = getAdminFirestore();
      console.log("✅ Firestore obtido com sucesso");
    } catch (initError) {
      console.error("❌ Erro ao obter instâncias do Firebase Admin:", initError);
      return NextResponse.json(
        {
          error: 'Erro ao inicializar serviços do Firebase',
          details: initError instanceof Error ? initError.message : 'Erro desconhecido',
          status: getFirebaseAdminStatus()
        },
        { status: 500 }
      );
    }

    console.log("🔐 Criando utilizador no Firebase Authentication...");
    const userRecord = await auth.createUser({
      email,
      password,
      displayName: fullName,
      emailVerified: false,
    });
    console.log("✅ Utilizador criado com sucesso:", userRecord.uid);

    // Preparar os dados do perfil do utilizador
    const userData = {
      fullName,
      registrationNumber,
      category,
      email,
      role: role || null,
      teamId: teamId || null,
      canEditProfile: canEditProfile !== false,
      profileCompleted: true, // O admin já preencheu o perfil
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Guardar os dados do utilizador no Firestore
    console.log("🗄️ Guardando dados do utilizador no Firestore...");
    await firestore.collection('users').doc(userRecord.uid).set(userData);
    console.log("✅ Dados do utilizador guardados com sucesso");

    // Se o utilizador for atribuído a uma equipa, atualizar a equipa
    if (teamId && (role === UserRole.TEAM_LEADER || role === UserRole.TEAM_MEMBER)) {
      console.log(`👥 Atualizando equipa ${teamId} para incluir o utilizador ${userRecord.uid}`);
      const teamRef = firestore.collection('teams').doc(teamId);
      const teamDoc = await teamRef.get();

      if (teamDoc.exists) {
        // Atualizar o array apropriado na equipa
        if (role === UserRole.TEAM_LEADER) {
          console.log(`Adicionando utilizador ${userRecord.uid} como líder da equipa ${teamId}`);
          await teamRef.update({
            leaders: admin.firestore.FieldValue.arrayUnion(userRecord.uid),
            updatedAt: new Date()
          });
        } else if (role === UserRole.TEAM_MEMBER) {
          console.log(`Adicionando utilizador ${userRecord.uid} como membro da equipa ${teamId}`);
          await teamRef.update({
            members: admin.firestore.FieldValue.arrayUnion(userRecord.uid),
            updatedAt: new Date()
          });
        }
        console.log(`Equipa ${teamId} atualizada com sucesso`);
      } else {
        console.warn(`Equipa ${teamId} não encontrada`);
      }
    }

    // Enviar email de convite (link para redefinir a senha)
    const actionCodeSettings = {
      url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login`,
      handleCodeInApp: false,
    };

    console.log("📧 Gerando link de redefinição de password...");
    const resetLink = await auth.generatePasswordResetLink(email, actionCodeSettings);
    console.log("✅ Link de redefinição de password gerado com sucesso");

    // Em produção, deve-se enviar o email usando um serviço de email
    // Aqui, apenas retornamos o link para fins de demonstração

    return NextResponse.json({
      success: true,
      message: 'Utilizador criado com sucesso',
      userId: userRecord.uid,
      resetLink,
    });
  } catch (error: any) {
    console.error('💥 Erro ao registar utilizador:', error);
    console.error('📋 Stack trace:', error.stack);
    console.error('📊 Status do Firebase Admin:', getFirebaseAdminStatus());

    // Tratar erros específicos do Firebase
    if (error.code === 'auth/email-already-exists') {
      return NextResponse.json(
        { error: 'O email já está em uso por outro utilizador' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/invalid-email') {
      return NextResponse.json(
        { error: 'O email fornecido é inválido' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/weak-password') {
      return NextResponse.json(
        { error: 'A password é muito fraca' },
        { status: 400 }
      );
    }

    // Verificar se é um erro de permissão
    if (error.code === 'auth/insufficient-permission' || error.message?.includes('Permission denied')) {
      return NextResponse.json(
        { error: 'Permissão negada. Verifique se as credenciais do Firebase Admin têm permissões suficientes.' },
        { status: 403 }
      );
    }

    // Verificar se é um erro de inicialização
    if (error.message?.includes('default Firebase app does not exist') ||
        error.message?.includes('initializeApp')) {
      return NextResponse.json(
        {
          error: 'Erro de inicialização do Firebase Admin',
          details: error.message,
          status: getFirebaseAdminStatus(),
          suggestion: 'Verifique as credenciais e configuração do Firebase Admin'
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        error: 'Erro ao registar utilizador',
        details: error.message,
        code: error.code,
        status: getFirebaseAdminStatus()
      },
      { status: 500 }
    );
  }
}
